[2025-09-12 02:07:12] local.INFO: Domain History: Domain deletion request approved by admin 1 (a@a.a)  
[2025-09-12 02:07:12] local.INFO: array (
  'domainId' => 173,
  'domainName' => 'klooty.net',
  'userID' => 7,
  'userId' => 7,
  'userEmail' => '<EMAIL>',
  'userName' => '<PERSON> Julius',
  'reason' => 'Mistaken Registration',
  'support_note' => 'Request approveed by a@a.a',
  'supportNote' => 'Request approveed by a@a.a',
  'createdDate' => '2025-09-11 02:47:58',
  'adminId' => 1,
  'adminName' => 'admin 1',
  'adminEmail' => 'a@a.a',
)  
[2025-09-12 03:21:51] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-admin\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-admin\\resources\\views\\app.blade.php)","code":0}  
[2025-09-12 03:30:28] local.INFO: Domain History: Domain deletion request approved by admin 1 (a@a.a)  
[2025-09-12 03:30:28] local.ERROR: {"query":[],"parameter":{"domainName":"klooty.org","userEmail":"<EMAIL>","domainId":175,"createdDate":"2025-09-11 02:45:04","userID":7,"email":"<EMAIL>"},"error":"ErrorException","message":"Undefined array key \"userID\"","code":0}  
[2025-09-12 03:30:31] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-12 03:31:43] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-12 03:32:12] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-12 03:32:23] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-12 03:34:30] local.INFO: ApprovalDeleteRequest: Running...  
[2025-09-12 03:34:31] local.INFO: ApprovalDeleteRequest: Processed 4 expired requests  
[2025-09-12 03:34:31] local.INFO: ApprovalDeleteRequest: Done  
[2025-09-12 03:35:14] local.ERROR: App\Modules\PendingDelete\Jobs\DomainEppDeletion has been attempted too many times.  
[2025-09-12 03:35:14] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\Queue\\MaxAttemptsExceededException","message":"App\\Modules\\PendingDelete\\Jobs\\DomainEppDeletion has been attempted too many times.","code":0}  
[2025-09-12 03:35:14] local.ERROR: App\Modules\PendingDelete\Jobs\DomainEppDeletion has been attempted too many times.  
[2025-09-12 03:35:14] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\Queue\\MaxAttemptsExceededException","message":"App\\Modules\\PendingDelete\\Jobs\\DomainEppDeletion has been attempted too many times.","code":0}  
[2025-09-12 03:35:14] local.ERROR: App\Modules\PendingDelete\Jobs\DomainEppDeletion has been attempted too many times.  
[2025-09-12 03:35:14] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\Queue\\MaxAttemptsExceededException","message":"App\\Modules\\PendingDelete\\Jobs\\DomainEppDeletion has been attempted too many times.","code":0}  
[2025-09-12 03:35:14] local.ERROR: App\Modules\PendingDelete\Jobs\DomainEppDeletion has been attempted too many times.  
[2025-09-12 03:35:14] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\Queue\\MaxAttemptsExceededException","message":"App\\Modules\\PendingDelete\\Jobs\\DomainEppDeletion has been attempted too many times.","code":0}  
[2025-09-12 03:35:14] local.ERROR: App\Modules\PendingDelete\Jobs\DomainEppDeletion has been attempted too many times.  
[2025-09-12 03:35:14] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\Queue\\MaxAttemptsExceededException","message":"App\\Modules\\PendingDelete\\Jobs\\DomainEppDeletion has been attempted too many times.","code":0}  
[2025-09-12 03:35:14] local.ERROR: App\Modules\PendingDelete\Jobs\DomainEppDeletion has been attempted too many times.  
[2025-09-12 03:35:14] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\Queue\\MaxAttemptsExceededException","message":"App\\Modules\\PendingDelete\\Jobs\\DomainEppDeletion has been attempted too many times.","code":0}  
[2025-09-12 03:35:47] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
